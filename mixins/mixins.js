import { mapGetters } from 'vuex'
import { mapState } from 'vuex'

export default {
  computed: {
    ...mapGetters({
      notify: ['localStorage/get_error_code'],
      userToken: ['localStorage/userToken'],
      userData: ['localStorage/userData'],
    }),
    ...mapState({
      branch_user: (state) => state.localStorage.branch_user,
    }),
    // userData() {
    //   return this.$cookies.get('userData')
    // },
  },
  methods: {
    productPrice(num) {
      const number = parseFloat(num)
      return number.toLocaleString(undefined, {
        minimumFractionDigits: 2,
      })
    },
    async logout() {
      setTimeout(() => {
        this.$store.commit('localStorage/RESET_USER')
        this.$store.commit('localStorage/RESET_BRANCH_USER')
      }, 500)
      this.TriggerNotify('success', this.$t('admin.logout_success'))
      this.$router.replace(this.localePath('/'))
    },
  },
}
