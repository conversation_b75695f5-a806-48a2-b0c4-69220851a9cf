<template>
  <div class="bid_form_wrapper">
    <client-only>
      <ValidationObserver ref="form">
        <b-form @submit.prevent="handleForm">
          <div class="row">
            <div class="col-lg-6">
              <ValidationProvider rules="required" v-slot="{ errors }">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.bid_form.name') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group>
                    <b-form-input type="text" v-model="form.name" :class="{ invalid: errors[0] }" :placeholder="$t('admin.enter') + ' ' + $t('admin.bid_form.name')
                      "></b-form-input>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
              </ValidationProvider>
            </div>
            <!-- end::col -->
            <div class="col-lg-6">
              <ValidationProvider rules="required" v-slot="{ errors }">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.bid_form.type') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group>
                    <multiselect :options="types" v-model="type_id" :placeholder="$t('admin.select') + ' ' + $t('admin.bid_form.type')
                      " track-by="value" label="name">
                    </multiselect>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
              </ValidationProvider>

              <ValidationProvider rules="required" v-slot="{ errors }" v-if="form.type_id === 'custom'">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.bid_form.type') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group>
                    <b-form-input type="text" v-model="form.other_type" :class="{ invalid: errors[0] }" :placeholder="$t('admin.enter') + ' ' + $t('admin.bid_form.name')
                      "></b-form-input>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
              </ValidationProvider>
            </div>
            <!-- end::col -->
            <div class="col-lg-6">
              <ValidationProvider rules="required" v-slot="{ errors }">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.bid_form.region') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group>
                    <multiselect :options="regions" v-model="form.region_id" :placeholder="$t('admin.select') + ' ' + $t('admin.bid_form.region')
                      " track-by="value" label="name">
                    </multiselect>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
              </ValidationProvider>
            </div>
            <!-- end::col -->
            <div class="col-lg-6">
              <ValidationProvider rules="required" v-slot="{ errors }">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.bid_form.expire_date') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group>
                    <date-picker
                      class="form-control"
                      style="margin-inline-end: 10px; width: calc(50% - 5px)"
                      type="date"
                      v-model="expires_at_date"
                      :confirm="true"
                      :placeholder="
                        $t('admin.enter') +
                        ' ' +
                        $t('admin.bid_form.expire_date')
                      "
                      format="YYYY-MM-DD"
                      :lang="lang"
                    >
                    </date-picker>

                    <vue-timepicker format="HH:mm:ss" style="width: calc(50% - 5px)" v-model="expires_at_time"
                      :hourLabel="$t('admin.hours')" :minuteLabel="$t('admin.mins')" :secondLabel="$t('admin.secds')"
                      :placeholder="$t('admin.enter') +
                        ' ' +
                        $t('admin.bid_form.expire_time')
                        "></vue-timepicker>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
              </ValidationProvider>
            </div>
            <!-- end::col -->
            <div class="col-lg-12">
              <ValidationProvider>
                <b-form-group>
                  <b-input-group class="chackbox_for_bid">
                    <input type="checkbox" name="prices_status" id="manage_pricing" v-model="pricing_check" />
                    <label for="manage_pricing">
                      {{ $t('admin.manage_pricing') }}
                    </label>
                  </b-input-group>
                </b-form-group>
              </ValidationProvider>
            </div>
            <!-- end::col -->
            <div class="col-lg-6" v-if="pricing_check == true">
              <ValidationProvider :rules="{ required: true }" v-slot="{ errors }">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.bid_form.pricing_from') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group class="flex-wrap">
                    <select class="form-control" v-model="form.pricing_time_from">
                      <option value="30">{{ $t('admin.half_hour') }}</option>
                      <option :value="(idx + 1) * 60" v-for="(item, idx) in 10" :key="idx + 1">
                        {{ idx + 1 + ' ' + $t('admin.hours') }}
                      </option>
                    </select>
                    <span class="pricing_hint w-100">
                      {{ $t('admin.pricing_hint') }}
                    </span>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
              </ValidationProvider>
            </div>
            <!-- end::col -->
            <!-- <div class="col-lg-12">
              <ValidationProvider>
                <b-form-group>
                  <b-input-group class="chackbox_for_bid">
                    <input
                      type="checkbox"
                      name="prices_status"
                      id="prices"
                      v-model="form.show_prices"
                    />
                    <label for="prices">
                      {{ $t('admin.bid_form.show_prices') }}
                    </label>
                  </b-input-group>
                </b-form-group>
              </ValidationProvider>
            </div> -->
            <!-- end::col -->
            <div class="col-lg-12">
              <ValidationProvider>
                <b-form-group>
                  <label class="control-label m-inline-end-20">
                    {{ $t('admin.bid_form.upload_invitation_attach') }}
                  </label>
                  <b-input-group class="align-items-center">
                    <div class="lg-file-picker">
                      <input type="file" id="attachments" @change="uploadFiles($event)" multiple />
                      <label for="attachments">
                        <svg class="icon">
                          <use xlink:href="~/static/sprite.svg#upload-circle"></use>
                        </svg>
                        <p>
                          <span> {{ $t('admin.click_to_upload') }} </span>
                          {{ $t('admin.drag_and_drop') }}
                        </p>
                        <p>{{ $t('admin.max_upload') }}</p>
                      </label>
                      <div class="preview_files lg" v-if="files_names.length > 0">
                        <div class="item" v-for="(file, idx) in files_names" :key="idx + 999">
                          <a :href="file.file">
                            {{ file.name }}
                          </a>
                          <button type="button" class="btn btn-link" @click="deleteMedia(file.id, idx)">
                            <svg class="icon">
                              <use xlink:href="~/static/sprite.svg#close"></use>
                            </svg>
                          </button>
                        </div>
                      </div>
                      <!-- end::preview_files -->
                    </div>
                  </b-input-group>
                </b-form-group>
                <!-- end::form-group -->
              </ValidationProvider>
            </div>
            <!-- end::col -->

            <div class="col-lg-12">
              <ValidationProvider v-slot="{ errors }">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.external_bid_invite_url') }}
                  </label>
                  <b-input-group>
                    <input type="text" class="form-control" @change="validateUrl($event.target.value, 'media_url')"
                      :placeholder="form.media_url" />
                  </b-input-group>
                </b-form-group>
              </ValidationProvider>
            </div>
            <!-- end::col -->

            <div class="col-lg-12">
              <ValidationProvider v-slot="{ errors }">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.private_bid_attachments') }}
                  </label>
                  <b-input-group>
                    <input type="text" class="form-control" @change="
                      validateUrl(
                        $event.target.value,
                        'private_attachments_url'
                      )
                      " :placeholder="form.private_attachments_url" />
                  </b-input-group>
                </b-form-group>
              </ValidationProvider>
            </div>
            <!-- end::col -->

            <div class="col-lg-12" v-if="item == null">
              <ValidationProvider v-slot="{ errors }">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.invite_seller') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group>
                    <template #prepend>
                      <svg class="icon" :class="{ invalid: errors[0] }">
                        <use xlink:href="~/static/sprite.svg#invite-user"></use>
                      </svg>
                    </template>
                    <client-only>
                      <vue-tags-input v-model="tag" :tags="addEmails" @tags-changed="(newTags) => (addEmails = newTags)"
                        :add-on-key="[13, ',']" :placeholder="$t('admin.custom_invited_emails')" class="form-control" />
                    </client-only>
                  </b-input-group>
                  <CustomTagsInput :filters="{
                    type_id: form.type_id ? form.type_id : null,
                    region_id: form.region_id ? form.region_id.id : null,
                  }" @handle-selected-email="handleSelectedEmail"></CustomTagsInput>
                  <span class="validation-error">
                    {{ emailsValidation }}
                  </span>
                </b-form-group>
              </ValidationProvider>
            </div>
            <!-- end::col -->
          </div>
          <!-- end::row -->

          <div class="card_footer">
            <div class="container-fluid">
              <div class="buttons_wrapper">
                <button type="submit" class="btn btn-default" :disabled="disabled" v-if="item == null">
                  <b-spinner variant="light" small v-if="disabled"></b-spinner>
                  <svg class="icon" v-if="!disabled">
                    <use xlink:href="~/static/sprite.svg#plus"></use>
                  </svg>
                  <span>{{ $t('admin.create_bid') }}</span>
                </button>
                <button type="submit" class="btn btn-default" :disabled="disabled" v-if="item != null">
                  <b-spinner variant="light" small v-if="disabled"></b-spinner>
                  <span>{{ $t('admin.update_bid') }}</span>
                </button>
              </div>
            </div>
          </div>
          <!-- end::footer_wrapper -->
        </b-form>
      </ValidationObserver>
    </client-only>
  </div>
</template>

<script src="~/pages/dashboard/mybids/-form/script.js"></script>

<style lang="scss" scoped>
.bid_form_wrapper {
  padding-bottom: 60px;
}

.pricing_hint {
  font-size: 14px;
  font-weight: 500;
  color: #667085;
}

.card_footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  padding-block: 20px;
  border-top: 1px solid #f2f4f7;
  z-index: 1001;

  @media (max-width: 991px) {
    padding-block: 10px;
  }

  .buttons_wrapper {
    width: 80%;
    margin-inline: auto;
    display: flex;
    justify-content: flex-end;

    .btn {
      .icon {
        width: 20px;
        height: 20px;
      }

      &:last-child {
        background-color: $base-color;
        color: #fff;
        border-radius: 8px;
      }
    }
  }
}
</style>

<style lang="scss">
.vue__time-picker input.display-time {
  height: 48px;
  border: 1px solid #d0d5dd;
  box-shadow: none;
  border-radius: 8px !important;
  width: 100%;
  font-family: 'IBM Plex Sans Arabic', sans-serif;
  font-size: 14px;
}

.vue__time-picker .dropdown .select-list,
.vue__time-picker-dropdown .select-list {
  direction: ltr;
}

.vue__time-picker .controls {
  right: 4px;
}

.vue__time-picker .dropdown ul li,
.vue__time-picker-dropdown ul li {
  font-size: 14px;
  font-weight: 400;
  font-family: 'IBM Plex Sans Arabic', sans-serif;
}

.vue__time-picker .dropdown ul li.hint,
.vue__time-picker-dropdown ul li.hint {
  color: #000;
  font-weight: 500;
}

.vue__time-picker .dropdown ul::-webkit-scrollbar {
  width: 3px;
}

.vue__time-picker .dropdown ul::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.vue__time-picker .dropdown ul::-webkit-scrollbar-thumb {
  background: $base-color;
  border-radius: 25px;
}

[dir='rtl'] {
  .vue__time-picker .controls {
    right: unset;
    left: 4px;
  }
}
</style>
