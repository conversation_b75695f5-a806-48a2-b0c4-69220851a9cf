import DatePicker from 'vue2-datepicker'
import 'vue2-datepicker/index.css'
import VueT<PERSON>picker from 'vue2-timepicker/src/vue-timepicker.vue'
// importing vuex tools
import { mapGetters } from 'vuex'
import CustomTagsInput from '~/components/dashboard/reuseable/CustomTagsInput.vue'
import ar from 'vue2-datepicker/locale/ar';
import en from 'vue2-datepicker/locale/en';

export default {
  name: 'BidForm',
  components: { DatePicker, VueTimepicker, CustomTagsInput },
  props: ['item'],
  data() {
    return {
      tag: '',
      lang: this.$i18n.locale === 'ar' ? ar : en,
      emails: [],
      addEmails: [],
      emailsValidation: '',
      attachments: [],
      files_names: [],
      form: {
        name: null,
        type_id: null,
        region_id: null,
        expires_at: null,
        show_prices: false,
        pricing_time_from: null,
        files: [],
        media_url: null,
        private_attachments_url: null,
        other_type: null,
      },
      expires_at_date: null,
      expires_at_time: {},
      pricing_check: false,
      disabled: false,
      type_id: null,
    }
  },
  created() {
    this.$moment.locale('en');
    if (this.item != null) {
      this.form.name = this.item.name
      this.type_id = this.item.type_id
      this.type_id = {
        id: this.item.type_id,
        name: this.item.type,
      }
      this.form.region_id = {
        id: this.item.region_id,
        name: this.item.region,
      }
      this.form.expires_at = this.$moment(this.item.expiresAt).format(
        'YYYY-MM-DD'
      )
      this.expires_at_date = this.$moment(this.item.expiresAt).toDate() // Convert to a Date object

      this.expires_at_time = {
        HH: this.$moment(this.item.expiresAt).format('HH'),
        mm: this.$moment(this.item.expiresAt).format('mm'),
        ss: this.$moment(this.item.expiresAt).format('ss'),
      }

      this.pricing_check = this.item.offer_times.selected_pricing_duration
        ? true
        : false

      this.form.pricing_time_from = this.$moment(
        this.item.offer_times.pricing_time_from
      ).format('YYYY-MM-DD hh:mm a')
      this.form.pricing_time_to = this.$moment(
        this.item.offer_times.pricing_time_to
      ).format('YYYY-MM-DD hh:mm a')
      this.form.show_prices = this.item.showPrices
      this.item.files.forEach((file) => {
        this.files_names.push(file)
      })
      this.item.invidedSellers.forEach((elem) => {
        this.emails.push({ text: elem })
      })
      this.form.media_url = this.item.media_url
      this.form.private_attachments_url = this.item.private_attachments_url
    }
  },
  mounted() {
    this.$moment.locale('en');
    // Set other type to types list
    this.$store.commit('dropdowns/SET_OTHER_TYPE_TO_TYPES', {
      id: 'custom',
      name: this.$t('admin.other'),
    })
  },
  watch: {
    pricing_check(current) {
      if (current == false) {
        this.form.pricing_time_from = null
      }
    },
    type_id(current) {
      if (current) {
        this.form.type_id = current.id
      }
    },
  },
  computed: {
    ...mapGetters({
      types: ['dropdowns/get_types'],
      regions: ['dropdowns/get_regions'],
    }),
  },
  methods: {
    handleSelectedEmail(emails) {
      this.emails = emails
    },
    validateUrl(value, type) {
      try {
        new URL(value)
        if (type == 'media_url') {
          this.form.media_url = value
        } else if (type == 'private_attachments_url') {
          this.form.private_attachments_url = value
        }
      } catch (err) {
        this.form.media_url = null
        this.TriggerNotify('error', 'يرجى ادخال مسار الملف بشكل صحيح !')
      }
    },
    async deleteMedia(id, idx) {
      if (id != 'new') {
        await this.$axios
          .delete(`/bids/deleteBidFile/${id}`)
          .then((res) => {
            this.TriggerNotify('success', 'تم حذف الملف بنجاح !')
          })
          .catch((err) => {
            this.TriggerNotify('error', this.notify.message)
          })
      }
      if (id == 'new') {
        this.form.files.splice(idx, 1)
      }
      this.files_names.splice(idx, 1)
    },
    uploadFiles($event) {
      const files = $event.target.files
      for (let x = 0; x < files.length; x++) {
        const imageExt = [
          'jpeg',
          'png',
          'jpg',
          'gif',
          'pdf',
          'docx',
          'xlsx',
          'csv',
          'xls',
          'dwg',
          'cad',
          'rvt',
        ]
        const extension = files[x].name.split('.').pop().toLowerCase()
        const size = files[x].size
        if (imageExt.includes(extension)) {
          if (size < 102400 * 1024) {
            this.form.files.push(files[x])
            this.files_names.push({
              id: 'new',
              name: files[x].name,
              file: URL.createObjectURL(files[x]),
            })
          } else {
            this.TriggerNotify('error', this.$t('admin.size_error'))
          }
        } else {
          this.TriggerNotify('error', this.$t('admin.extension_error'))
        }
      }
    },
    async handleForm() {
      await this.$refs.form.validate().then((success) => {
        if (success) {
          const invitedEmails = this.emails.length + this.addEmails.length
          if (invitedEmails < 2) {
            this.emailsValidation = this.$t('admin.invite_bidders_validation')
          } else {
            this.emailsValidation = null
            this.handleReq()
          }
        }
      })
    },
    async handleReq() {
     this.disabled = true

      const form_data = new FormData()
      form_data.append('name', this.form.name)
      if (this.form.type_id != 'custom') {
        form_data.append('type_id', this.form.type_id)
      } else {
        form_data.append('other_type', this.form.other_type)
      }
      const formattedDate = this.$moment(this.expires_at_date).format('YYYY-MM-DD');

      form_data.append(
        'expires_at',
        `${formattedDate} ${ // Use the newly formatted string
          this.expires_at_time.HH ? this.expires_at_time.HH : '00'
        }:${this.expires_at_time.mm ? this.expires_at_time.mm : '00'}:${
          this.expires_at_time.ss ? this.expires_at_time.ss : '00'
        }`
      )
      if (this.item != null) {
        if (this.item.offer_times.selected_pricing_duration) {
          form_data.append(
            'pricing_duration',
            this.item.offer_times.selected_pricing_duration
          )
        }
      } else {
        if (this.form.pricing_time_from != null) {
          form_data.append('pricing_duration', this.form.pricing_time_from)
        }
      }
      form_data.append('region_id', this.form.region_id.id)
      if (this.form.media_url != null) {
        form_data.append('media_url', this.form.media_url)
      }
      if (this.form.private_attachments_url != null) {
        form_data.append(
          'private_attachments_url',
          this.form.private_attachments_url
        )
      }
      form_data.append('show_prices', this.form.show_prices == true ? 1 : 0)
      if (this.form.files.length > 0) {
        this.form.files.forEach((file) => {
          form_data.append('files[]', file)
        })
      }
      if (this.item == null) {
        this.emails.forEach((email) => {
          form_data.append('bidders[]', email)
        })
        if (this.addEmails.length > 0) {
          this.addEmails.forEach((email) => {
            form_data.append('bidders[]', email.text)
          })
        }
      }

      if (this.item == null) {
        await this.$axios.post('/bids', form_data).then((res) => {
          // console.log(res)
          this.$store.dispatch('localStorage/response_handler', res.data)
          if (this.notify.state == 0) {
            this.TriggerNotify(
              'success',
              this.$t('admin.bid_form.success_added_bid')
            )
            this.$router.push(this.localePath('/dashboard/mybids'))
          } else {
            this.TriggerNotify('error', this.notify.message)
          }
        })
      } else {
        await this.$axios
          .post(`/bids/${this.$route.params.id}/update`, form_data)
          .then((res) => {
            // console.log(res)
            this.$store.dispatch('localStorage/response_handler', res.data)
            if (this.notify.state == 0) {
              this.TriggerNotify(
                'success',
                this.$t('admin.bid_form.success_updated_bid')
              )
              this.$router.push(this.localePath('/dashboard/mybids'))
            } else {
              this.TriggerNotify('error', this.notify.message)
            }
          })
      }
      this.disabled = false
    },
  },
  beforeDestroy() {
    this.$moment.locale(this.$i18n.locale);
  }
}
